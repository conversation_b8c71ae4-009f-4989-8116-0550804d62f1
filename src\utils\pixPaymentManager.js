import { MercadoPagoConfig, Payment } from 'mercadopago';
import QRCode from 'qrcode';
import { AttachmentBuilder } from 'discord.js';
import { logger } from './logger.js';
import BotConfig from '../models/BotConfig.js';

/**
 * Gerenciador de pagamentos PIX via MercadoPago
 */
export class PixPaymentManager {
    constructor() {
        this.client = null;
        this.activePollings = new Map(); // Map para controlar polling ativo por paymentId
    }

    /**
     * Inicializa o cliente MercadoPago para uma guild específica
     * @param {string} guildId - ID da guild
     * @returns {Promise<boolean>} - True se inicializado com sucesso
     */
    async initializeClient(guildId) {
        try {
            const config = await BotConfig.findByGuild(guildId);

            if (!config || !config.mercadoPago || !config.mercadoPago.isEnabled) {
                throw new Error('MercadoPago não configurado para esta guild');
            }

            if (!config.mercadoPago.accessToken) {
                throw new Error('Access Token do MercadoPago não configurado');
            }

            // Detecta ambiente baseado no token
            const isSandbox = config.mercadoPago.accessToken.startsWith('TEST-');
            const environment = isSandbox ? 'sandbox' : 'production';

            this.client = new MercadoPagoConfig({
                accessToken: config.mercadoPago.accessToken,
                options: {
                    timeout: 10000, // Aumentado para 10 segundos
                    idempotencyKey: `guild_${guildId}_${Date.now()}` // Chave única por guild e timestamp
                }
            });

            logger.info(`Cliente MercadoPago inicializado para guild ${guildId} - Ambiente: ${environment}`);
            return true;

        } catch (error) {
            logger.error('Erro ao inicializar cliente MercadoPago:', error);
            return false;
        }
    }

    /**
     * Testa a conectividade com a API do MercadoPago usando múltiplas abordagens
     * @param {string} guildId - ID da guild
     * @returns {Promise<Object>} - Resultado do teste
     */
    async testApiConnection(guildId) {
        try {
            const config = await BotConfig.findByGuild(guildId);

            if (!config || !config.mercadoPago || !config.mercadoPago.isEnabled) {
                return {
                    success: false,
                    error: 'MercadoPago não configurado para esta guild',
                    details: 'Configure o MercadoPago primeiro usando o comando /configbot'
                };
            }

            if (!config.mercadoPago.accessToken) {
                return {
                    success: false,
                    error: 'Access Token não configurado',
                    details: 'Configure o Access Token do MercadoPago'
                };
            }

            // Detecta ambiente
            const isSandbox = config.mercadoPago.accessToken.startsWith('TEST-');
            const environment = isSandbox ? 'sandbox' : 'production';

            // Inicializa cliente temporário para teste
            logger.info('Inicializando cliente MercadoPago para teste:', {
                tokenPrefix: config.mercadoPago.accessToken.substring(0, 10),
                environment: environment,
                guildId: guildId
            });

            const testClient = new MercadoPagoConfig({
                accessToken: config.mercadoPago.accessToken,
                options: {
                    timeout: 10000,
                    // Adiciona headers customizados para debug
                    customHeaders: {
                        'User-Agent': 'Discord-Bot-PIX/1.0'
                    }
                }
            });

            logger.info('Cliente MercadoPago inicializado para teste');

            // Primeira abordagem: Testa usando o SDK do MercadoPago
            const sdkTestResult = await this.testWithSDK(testClient, environment, isSandbox);
            if (sdkTestResult.success || sdkTestResult.definitive) {
                return sdkTestResult;
            }

            // Segunda abordagem: Testa usando requisição HTTP direta
            logger.info('Teste com SDK falhou, tentando requisição HTTP direta...');
            const httpTestResult = await this.testWithDirectHTTP(config.mercadoPago.accessToken, environment, isSandbox);

            return httpTestResult;

        } catch (error) {
            logger.error('Erro ao testar conectividade com MercadoPago:', error);

            return {
                success: false,
                error: 'Erro interno ao testar conectividade',
                details: error.message
            };
        }
    }

    /**
     * Testa conectividade usando o SDK do MercadoPago
     * @param {MercadoPagoConfig} testClient - Cliente configurado
     * @param {string} environment - Ambiente (sandbox/production)
     * @param {boolean} isSandbox - Se é ambiente sandbox
     * @returns {Promise<Object>} - Resultado do teste
     */
    async testWithSDK(testClient, environment, isSandbox) {
        try {
            const payment = new Payment(testClient);

            logger.info(`Testando conectividade da API MercadoPago com SDK - Ambiente: ${environment}`);

            // Tenta buscar um pagamento inexistente para testar a API
            const testResult = await payment.get({ id: 'test_connection_123456789' });

            // Se chegou aqui sem erro, algo inesperado aconteceu
            logger.warn('API MercadoPago retornou resposta inesperada para pagamento inexistente:', testResult);

            return {
                success: true,
                environment: environment,
                message: `API do MercadoPago respondeu inesperadamente (${environment})`,
                details: {
                    status: 'Resposta inesperada da API',
                    environment: environment,
                    tokenType: isSandbox ? 'Sandbox (Teste)' : 'Produção',
                    method: 'SDK'
                }
            };

        } catch (apiError) {
            return this.processApiError(apiError, environment, isSandbox, 'SDK');
        }
    }

    /**
     * Testa conectividade usando requisição HTTP direta
     * @param {string} accessToken - Token de acesso
     * @param {string} environment - Ambiente (sandbox/production)
     * @param {boolean} isSandbox - Se é ambiente sandbox
     * @returns {Promise<Object>} - Resultado do teste
     */
    async testWithDirectHTTP(accessToken, environment, isSandbox) {
        try {
            logger.info(`Testando conectividade da API MercadoPago com HTTP direto - Ambiente: ${environment}`);

            // Importa fetch dinamicamente
            const fetch = (await import('node-fetch')).default;

            const baseUrl = isSandbox ? 'https://api.mercadopago.com' : 'https://api.mercadopago.com';
            const testUrl = `${baseUrl}/v1/payments/test_connection_123456789`;

            const response = await fetch(testUrl, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${accessToken}`,
                    'Content-Type': 'application/json',
                    'User-Agent': 'Discord-Bot-PIX/1.0'
                },
                timeout: 10000
            });

            logger.info(`Resposta HTTP recebida:`, {
                status: response.status,
                statusText: response.statusText,
                headers: Object.fromEntries(response.headers.entries())
            });

            // Erro 404 é esperado e indica que a API está funcionando
            if (response.status === 404) {
                return {
                    success: true,
                    environment: environment,
                    message: `Conectividade com API do MercadoPago confirmada (${environment})`,
                    details: {
                        status: 'API respondendo corretamente',
                        environment: environment,
                        tokenType: isSandbox ? 'Sandbox (Teste)' : 'Produção',
                        testResult: 'Erro 404 esperado recebido',
                        method: 'HTTP Direto'
                    }
                };
            }

            // Outros códigos de status
            const responseText = await response.text();

            return {
                success: false,
                error: `Resposta inesperada da API (${response.status})`,
                details: `Status: ${response.status} ${response.statusText}. Resposta: ${responseText.substring(0, 200)}`,
                debugInfo: {
                    statusCode: response.status,
                    statusText: response.statusText,
                    method: 'HTTP Direto',
                    responsePreview: responseText.substring(0, 200)
                }
            };

        } catch (error) {
            logger.error('Erro no teste HTTP direto:', error);

            return {
                success: false,
                error: 'Erro na requisição HTTP direta',
                details: error.message,
                debugInfo: {
                    errorType: 'HTTP Request Error',
                    message: error.message,
                    method: 'HTTP Direto',
                    suggestion: 'Verifique se o node-fetch está instalado: npm install node-fetch'
                }
            };
        }
    }

    /**
     * Processa erros da API do MercadoPago
     * @param {Error} apiError - Erro da API
     * @param {string} environment - Ambiente
     * @param {boolean} isSandbox - Se é sandbox
     * @param {string} method - Método usado (SDK/HTTP)
     * @returns {Object} - Resultado processado
     */
    processApiError(apiError, environment, isSandbox, method) {
        // Log detalhado do erro para debug
        logger.info('Erro capturado da API MercadoPago:', {
            status: apiError.status,
            statusText: apiError.statusText,
            message: apiError.message,
            cause: apiError.cause,
            response: apiError.response?.data,
            method: method,
            config: {
                url: apiError.config?.url,
                method: apiError.config?.method,
                headers: apiError.config?.headers ? 'Present' : 'Missing'
            }
        });

        // Verifica se o erro tem status definido
        const statusCode = apiError.status || apiError.response?.status || 'unknown';
        const statusText = apiError.statusText || apiError.response?.statusText || '';
        const errorMessage = apiError.message || 'Erro desconhecido';

        // Erro 404 é esperado e indica que a API está funcionando
        if (statusCode === 404) {
            return {
                success: true,
                environment: environment,
                message: `Conectividade com API do MercadoPago confirmada (${environment})`,
                details: {
                    status: 'API respondendo corretamente',
                    environment: environment,
                    tokenType: isSandbox ? 'Sandbox (Teste)' : 'Produção',
                    testResult: 'Erro 404 esperado recebido',
                    method: method
                }
            };
        }

        // Problemas de autenticação
        if (statusCode === 401) {
            return {
                success: false,
                error: 'Token de acesso inválido ou expirado',
                details: `Código HTTP: ${statusCode}. Verifique se o Access Token está correto e não expirou. ${statusText}`,
                definitive: true, // Não tenta outros métodos
                debugInfo: {
                    statusCode: statusCode,
                    statusText: statusText,
                    message: errorMessage,
                    method: method
                }
            };
        }

        // Problemas de permissão
        if (statusCode === 403) {
            return {
                success: false,
                error: 'Token sem permissões necessárias',
                details: `Código HTTP: ${statusCode}. O token não tem permissões para acessar a API. ${statusText}`,
                definitive: true, // Não tenta outros métodos
                debugInfo: {
                    statusCode: statusCode,
                    statusText: statusText,
                    message: errorMessage,
                    method: method
                }
            };
        }

        // Problemas de rede ou timeout
        if (statusCode === 'unknown' || !statusCode) {
            // Verifica se é erro de rede
            if (errorMessage.includes('ENOTFOUND') || errorMessage.includes('ECONNREFUSED')) {
                return {
                    success: false,
                    error: 'Erro de conectividade de rede',
                    details: 'Não foi possível conectar à API do MercadoPago. Verifique sua conexão com a internet.',
                    debugInfo: {
                        errorType: 'Network Error',
                        message: errorMessage,
                        method: method,
                        suggestion: 'Verifique firewall e conexão com a internet'
                    }
                };
            }

            // Verifica se é timeout
            if (errorMessage.includes('timeout')) {
                return {
                    success: false,
                    error: 'Timeout na conexão com a API',
                    details: 'A API do MercadoPago não respondeu dentro do tempo limite.',
                    debugInfo: {
                        errorType: 'Timeout Error',
                        message: errorMessage,
                        method: method,
                        suggestion: 'Tente novamente em alguns minutos'
                    }
                };
            }

            // Erro desconhecido sem status
            return {
                success: false,
                error: 'Erro desconhecido na comunicação com a API',
                details: `Erro sem código de status definido: ${errorMessage}`,
                debugInfo: {
                    errorType: 'Unknown Error',
                    message: errorMessage,
                    hasStatus: false,
                    method: method,
                    suggestion: 'Verifique os logs do servidor para mais detalhes'
                }
            };
        }

        // Outros códigos de erro HTTP
        return {
            success: false,
            error: `Erro na API do MercadoPago (${statusCode})`,
            details: `${statusText || 'Erro HTTP'}: ${errorMessage}`,
            debugInfo: {
                statusCode: statusCode,
                statusText: statusText,
                message: errorMessage,
                method: method,
                suggestion: 'Consulte a documentação da API do MercadoPago para este código de erro'
            }
        };
    }

    /**
     * Cria um pagamento PIX
     * @param {Object} paymentData - Dados do pagamento
     * @param {string} guildId - ID da guild
     * @returns {Promise<Object>} - Dados do pagamento criado
     */
    async createPixPayment(paymentData, guildId) {
        try {
            const initialized = await this.initializeClient(guildId);
            if (!initialized) {
                throw new Error('Falha ao inicializar cliente MercadoPago');
            }

            const payment = new Payment(this.client);

            // Detecta ambiente para usar dados apropriados
            const config = await BotConfig.findByGuild(guildId);
            const isSandbox = config.mercadoPago.accessToken.startsWith('TEST-');

            const paymentRequest = {
                transaction_amount: paymentData.amount,
                description: paymentData.description || 'Compra na loja Discord',
                payment_method_id: 'pix',
                payer: {
                    email: paymentData.payerEmail || `${paymentData.externalReference}@discord.com`,
                    first_name: paymentData.payerName || 'Cliente',
                    last_name: 'Discord',
                    identification: {
                        type: 'CPF',
                        // Usa CPF de teste apenas em sandbox, em produção usa um genérico válido
                        number: isSandbox ? '12345678909' : this.generateValidCPF()
                    }
                },
                notification_url: paymentData.webhookUrl,
                external_reference: paymentData.externalReference,
                date_of_expiration: new Date(Date.now() + 15 * 60 * 1000).toISOString() // 15 minutos
            };

            logger.info(`Criando pagamento PIX - Ambiente: ${isSandbox ? 'sandbox' : 'production'}, Valor: R$ ${paymentData.amount}`);

            const result = await payment.create({ body: paymentRequest });

            logger.info(`Pagamento PIX criado: ${result.id} - Status: ${result.status}`);

            const qrCodeString = result.point_of_interaction?.transaction_data?.qr_code;
            const qrCodeBase64 = result.point_of_interaction?.transaction_data?.qr_code_base64;
            const ticketUrl = result.point_of_interaction?.transaction_data?.ticket_url;
            let qrCodeAttachment = null;

            // Log detalhado dos dados do PIX
            logger.info('Dados do PIX recebidos:', {
                paymentId: result.id,
                hasQRCode: !!qrCodeString,
                hasQRCodeBase64: !!qrCodeBase64,
                hasTicketUrl: !!ticketUrl,
                qrCodeLength: qrCodeString?.length || 0,
                isValidQRCode: this.validatePixQRCode(qrCodeString)
            });

            // Gera QR code como imagem se disponível
            if (qrCodeString) {
                if (!this.validatePixQRCode(qrCodeString)) {
                    logger.warn(`QR Code PIX pode estar inválido para pagamento ${result.id}:`, {
                        qrCodeLength: qrCodeString.length,
                        qrCodeStart: qrCodeString.substring(0, 20)
                    });
                }

                try {
                    const qrCodeBuffer = await QRCode.toBuffer(qrCodeString, {
                        type: 'png',
                        width: 300,
                        margin: 2,
                        color: {
                            dark: '#000000',
                            light: '#FFFFFF'
                        }
                    });

                    qrCodeAttachment = new AttachmentBuilder(qrCodeBuffer, {
                        name: 'qr-code-pix.png',
                        description: 'QR Code para pagamento PIX'
                    });

                    logger.info(`QR Code gerado com sucesso para pagamento ${result.id}`);
                } catch (qrError) {
                    logger.error(`Erro ao gerar QR Code para pagamento ${result.id}:`, qrError);
                }
            } else {
                logger.error(`QR Code PIX não foi retornado pela API para pagamento ${result.id}`, {
                    pointOfInteraction: !!result.point_of_interaction,
                    transactionData: !!result.point_of_interaction?.transaction_data,
                    fullResponse: JSON.stringify(result, null, 2)
                });
            }

            // Validação final dos dados retornados
            const pixData = {
                id: result.id,
                status: result.status,
                qrCode: qrCodeString,
                qrCodeBase64: qrCodeBase64,
                qrCodeAttachment: qrCodeAttachment,
                ticketUrl: ticketUrl,
                expirationDate: result.date_of_expiration,
                amount: result.transaction_amount,
                // Informações adicionais para debug
                debug: {
                    hasQRCode: !!qrCodeString,
                    hasQRCodeBase64: !!qrCodeBase64,
                    hasTicketUrl: !!ticketUrl,
                    qrCodeValid: this.validatePixQRCode(qrCodeString),
                    environment: isSandbox ? 'sandbox' : 'production'
                }
            };

            // Log final com resumo
            logger.info(`Pagamento PIX criado com sucesso:`, {
                paymentId: result.id,
                amount: result.transaction_amount,
                status: result.status,
                environment: isSandbox ? 'sandbox' : 'production',
                qrCodeGenerated: !!qrCodeAttachment,
                qrCodeValid: this.validatePixQRCode(qrCodeString)
            });

            return pixData;

        } catch (error) {
            logger.error('Erro ao criar pagamento PIX:', error);
            throw error;
        }
    }

    /**
     * Verifica o status de um pagamento
     * @param {string} paymentId - ID do pagamento
     * @param {string} guildId - ID da guild
     * @returns {Promise<Object>} - Status do pagamento
     */
    async checkPaymentStatus(paymentId, guildId) {
        try {
            const initialized = await this.initializeClient(guildId);
            if (!initialized) {
                throw new Error('Falha ao inicializar cliente MercadoPago');
            }

            const payment = new Payment(this.client);
            const result = await payment.get({ id: paymentId });

            return {
                id: result.id,
                status: result.status,
                statusDetail: result.status_detail,
                approved: result.status === 'approved',
                rejected: result.status === 'rejected',
                cancelled: result.status === 'cancelled',
                pending: result.status === 'pending'
            };

        } catch (error) {
            logger.error(`Erro ao verificar status do pagamento ${paymentId}:`, error);
            throw error;
        }
    }

    /**
     * Inicia polling automático para verificar status do pagamento
     * @param {string} paymentId - ID do pagamento
     * @param {string} guildId - ID da guild
     * @param {Function} onStatusChange - Callback chamado quando status muda
     * @param {Object} options - Opções do polling
     * @returns {Promise<void>}
     */
    async startPaymentPolling(paymentId, guildId, onStatusChange, options = {}) {
        const {
            interval = 10000, // 10 segundos (mais frequente para melhor UX)
            maxAttempts = 90,  // 15 minutos total (90 * 10s)
            onTimeout = null
        } = options;

        // Evita polling duplicado
        if (this.activePollings.has(paymentId)) {
            logger.warn(`Polling já ativo para pagamento ${paymentId}`);
            return;
        }

        let attempts = 0;
        let lastStatus = null;
        let lastNotificationTime = 0;
        let consecutiveErrors = 0;
        const notificationInterval = 3 * 60 * 1000; // 3 minutos (mais frequente)
        const maxConsecutiveErrors = 5; // Máximo de erros consecutivos antes de parar

        logger.info(`Iniciando polling para pagamento ${paymentId} - Intervalo: ${interval}ms, Máximo: ${maxAttempts} tentativas`);

        const pollInterval = setInterval(async () => {
            try {
                attempts++;
                const currentTime = Date.now();

                logger.debug(`Polling tentativa ${attempts}/${maxAttempts} para pagamento ${paymentId}`);

                const status = await this.checkPaymentStatus(paymentId, guildId);

                // Reset contador de erros em caso de sucesso
                consecutiveErrors = 0;

                // Log detalhado do status
                logger.debug(`Status do pagamento ${paymentId}:`, {
                    status: status.status,
                    statusDetail: status.statusDetail,
                    approved: status.approved,
                    rejected: status.rejected,
                    cancelled: status.cancelled,
                    pending: status.pending
                });

                // Chama callback se status mudou
                if (status.status !== lastStatus) {
                    logger.info(`Status do pagamento ${paymentId} mudou de '${lastStatus}' para '${status.status}'`);
                    lastStatus = status.status;
                    await onStatusChange(status);
                }

                // Para polling se pagamento foi finalizado (com logs detalhados)
                if (status.approved || status.rejected || status.cancelled) {
                    this.stopPaymentPolling(paymentId);
                    logger.info(`Polling finalizado para pagamento ${paymentId} - Status final: ${status.status} (${status.statusDetail})`);
                    return;
                }

                // Envia notificação periódica para manter usuário informado
                if (currentTime - lastNotificationTime >= notificationInterval) {
                    lastNotificationTime = currentTime;
                    const remainingMinutes = Math.max(0, Math.ceil((maxAttempts - attempts) * interval / 60000));

                    logger.debug(`Enviando atualização de progresso para pagamento ${paymentId} - ${remainingMinutes} minutos restantes`);

                    await onStatusChange({
                        ...status,
                        isProgressUpdate: true,
                        remainingMinutes: remainingMinutes,
                        attempts: attempts
                    });
                }

                // Para polling se atingiu máximo de tentativas (pagamento expirou)
                if (attempts >= maxAttempts) {
                    this.stopPaymentPolling(paymentId);
                    logger.warn(`Polling expirado para pagamento ${paymentId} após ${attempts} tentativas - Status final: ${status.status}`);

                    if (onTimeout) {
                        await onTimeout();
                    }
                    return;
                }

            } catch (error) {
                consecutiveErrors++;
                logger.error(`Erro no polling do pagamento ${paymentId} (tentativa ${attempts}, erro consecutivo ${consecutiveErrors}):`, {
                    error: error.message,
                    status: error.status,
                    stack: error.stack
                });

                // Para polling apenas em caso de muitos erros consecutivos
                if (consecutiveErrors >= maxConsecutiveErrors) {
                    this.stopPaymentPolling(paymentId);
                    logger.error(`Parando polling para pagamento ${paymentId} - ${consecutiveErrors} erros consecutivos`);

                    // Notifica sobre o erro
                    try {
                        await onStatusChange({
                            error: true,
                            message: 'Erro ao verificar status do pagamento. Verifique manualmente.',
                            details: error.message
                        });
                    } catch (callbackError) {
                        logger.error('Erro ao notificar callback sobre erro no polling:', callbackError);
                    }
                    return;
                }

                // Para polling se pagamento não foi encontrado (404)
                if (error.status === 404) {
                    this.stopPaymentPolling(paymentId);
                    logger.warn(`Parando polling - pagamento ${paymentId} não encontrado (404)`);
                    return;
                }
            }
        }, interval);

        // Armazena referência do polling com mais informações
        this.activePollings.set(paymentId, {
            interval: pollInterval,
            startTime: Date.now(),
            attempts: 0,
            maxAttempts: maxAttempts,
            intervalMs: interval,
            guildId: guildId,
            consecutiveErrors: 0
        });

        logger.info(`Polling configurado para pagamento ${paymentId} - Verificará por ${(maxAttempts * interval) / 60000} minutos`);
    }

    /**
     * Para o polling de um pagamento específico
     * @param {string} paymentId - ID do pagamento
     */
    stopPaymentPolling(paymentId) {
        const polling = this.activePollings.get(paymentId);
        if (polling) {
            clearInterval(polling.interval);
            this.activePollings.delete(paymentId);
            logger.info(`Polling parado para pagamento ${paymentId}`);
        }
    }

    /**
     * Para todos os pollings ativos
     */
    stopAllPollings() {
        for (const [paymentId, polling] of this.activePollings) {
            clearInterval(polling.interval);
            logger.info(`Polling parado para pagamento ${paymentId}`);
        }
        this.activePollings.clear();
    }

    /**
     * Obtém informações sobre pollings ativos
     * @returns {Array} - Lista de pollings ativos
     */
    getActivePollings() {
        const pollings = [];
        for (const [paymentId, polling] of this.activePollings) {
            pollings.push({
                paymentId,
                startTime: polling.startTime,
                duration: Date.now() - polling.startTime,
                attempts: polling.attempts
            });
        }
        return pollings;
    }

    /**
     * Gera um CPF válido para uso em produção
     * @returns {string} - CPF válido
     */
    generateValidCPF() {
        // Gera os 9 primeiros dígitos
        const digits = [];
        for (let i = 0; i < 9; i++) {
            digits.push(Math.floor(Math.random() * 9));
        }

        // Calcula o primeiro dígito verificador
        let sum = 0;
        for (let i = 0; i < 9; i++) {
            sum += digits[i] * (10 - i);
        }
        let firstDigit = 11 - (sum % 11);
        if (firstDigit >= 10) firstDigit = 0;
        digits.push(firstDigit);

        // Calcula o segundo dígito verificador
        sum = 0;
        for (let i = 0; i < 10; i++) {
            sum += digits[i] * (11 - i);
        }
        let secondDigit = 11 - (sum % 11);
        if (secondDigit >= 10) secondDigit = 0;
        digits.push(secondDigit);

        return digits.join('');
    }

    /**
     * Valida se um QR Code PIX está no formato correto
     * @param {string} qrCode - String do QR Code
     * @returns {boolean} - True se válido
     */
    validatePixQRCode(qrCode) {
        if (!qrCode || typeof qrCode !== 'string') {
            return false;
        }

        // QR Code PIX deve começar com identificador específico
        // e ter pelo menos 100 caracteres (formato padrão)
        return qrCode.length >= 100 &&
               (qrCode.startsWith('00020126') || qrCode.startsWith('00020101'));
    }

    /**
     * Obtém informações detalhadas sobre um pagamento para debug
     * @param {string} paymentId - ID do pagamento
     * @param {string} guildId - ID da guild
     * @returns {Promise<Object>} - Informações detalhadas
     */
    async getPaymentDebugInfo(paymentId, guildId) {
        try {
            const initialized = await this.initializeClient(guildId);
            if (!initialized) {
                return { error: 'Falha ao inicializar cliente MercadoPago' };
            }

            const payment = new Payment(this.client);
            const result = await payment.get({ id: paymentId });

            return {
                id: result.id,
                status: result.status,
                statusDetail: result.status_detail,
                transactionAmount: result.transaction_amount,
                dateCreated: result.date_created,
                dateOfExpiration: result.date_of_expiration,
                externalReference: result.external_reference,
                paymentMethodId: result.payment_method_id,
                qrCodeData: {
                    qrCode: result.point_of_interaction?.transaction_data?.qr_code,
                    qrCodeBase64: result.point_of_interaction?.transaction_data?.qr_code_base64,
                    ticketUrl: result.point_of_interaction?.transaction_data?.ticket_url,
                    isValidQRCode: this.validatePixQRCode(result.point_of_interaction?.transaction_data?.qr_code)
                },
                payer: {
                    email: result.payer?.email,
                    firstName: result.payer?.first_name,
                    lastName: result.payer?.last_name,
                    identification: result.payer?.identification
                }
            };

        } catch (error) {
            logger.error(`Erro ao obter informações de debug do pagamento ${paymentId}:`, error);
            return {
                error: error.message,
                status: error.status || 'unknown',
                details: error.cause || 'Erro desconhecido'
            };
        }
    }
}

// Instância singleton
export const pixPaymentManager = new PixPaymentManager();
